package cn.sdata.om.al.job;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.DisclosureMethod;
import cn.sdata.om.al.enums.MailContentHandler;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.LogCustodianRecordsService;
import cn.sdata.om.al.service.LogMailSendRecordsService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.mail.MailInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;

@Slf4j
@Component
@AllArgsConstructor
public class MailSendJob implements QuartzJobBean {

    private final MailInfoService mailInfoService;
    private final CronService cronService;
    private final NetValueDisclosureService netValueDisclosureService;
    private final LogMailSendRecordsService logMailSendRecordsService;
    private final LogCustodianRecordsService logCustodianRecordsService;


    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "JobExecutionContext is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String dataDate = (String) mergedJobDataMap.get(DATA_DATE);
        Map<String, RemoteFileInfo> infoMap = (Map<String, RemoteFileInfo>) mergedJobDataMap.get(REMOTE_FILE);
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) mergedJobDataMap.get(NET_VALUE_DISCLOSURE_LIST);
        if (list != null && !list.isEmpty()) {
            Map<String, NetValueDisclosure> productIdNetValue = list.stream().collect(Collectors.toMap(NetValueDisclosure::getProductId,
                    netValueDisclosure -> netValueDisclosure,
                    (oldOne, newOne) -> newOne));
            List<String> productIds = (List<String>) mergedJobDataMap.get(PRODUCT_ID);
            Cron cron = cronService.getById(jobId);
            try {
                List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(productIds), infoMap);

                // 不再在这里设置SENDING状态，因为在调用方法中已经设置
                sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
                List<LogCustodianRecords> logCustodianRecordsList = new ArrayList<>();
                List<LogMailSendRecords> logMailSendRecordsList = new ArrayList<>();
                for (SendMailInfo sendMailInfo : sendMailInfos) {
                    List<String> mailProductIds = sendMailInfo.getProductIds();
                    for (String mailProductId : mailProductIds) {
                        NetValueDisclosure netValueDisclosure = productIdNetValue.get(mailProductId);
                        if (netValueDisclosure != null) {
                            dealNetValueDisclosure(netValueDisclosure, sendMailInfo, cron, logCustodianRecordsList, logMailSendRecordsList);
                        }
                    }
                }
                netValueDisclosureService.saveOrUpdateBatch(list);
                log.info("修改净值披露状态:{}", list);
                logCustodianRecordsService.saveBatch(logCustodianRecordsList);
                logMailSendRecordsService.saveBatch(logMailSendRecordsList);

            } catch (Exception e) {
                log.error("邮件发送任务执行失败", e);
                // 如果是净值披露邮件发送失败，需要回滚锁定状态
                String className = cron.getClassName();
                if (className != null && className.contains("NetValueDisclosureMailSendJob")) {
                    rollbackDisclosureLock(list);
                }
                throw e;
            }
        }
    }

    private void dealMailStatus(NetValueDisclosure netValueDisclosure, Cron cron){
        Objects.requireNonNull(netValueDisclosure, "净值披露不得为空");
        Objects.requireNonNull(cron, "调度任务不得为空");
        String jobName = cron.getClassName();
        switch (jobName){
            case "CustodianBankMailSendJob":
                String custodyReconciliationEmailSent = netValueDisclosure.getCustodyReconciliationEmailSent();
                if (!MailStatus.NONE.name().equals(custodyReconciliationEmailSent)) {
                    netValueDisclosure.setCustodyReconciliationEmailSent(MailStatus.SENDING.name());
                }
                break;
            case "InvestorMailSendJob":
                netValueDisclosure.setValuationTableSent(MailStatus.SENDING.name());
                break;
            case "ThirdMailSendJob":
                netValueDisclosure.setThirdPartySent(MailStatus.SENDING.name());
            case "InvestorStatementMailSendJob":
                netValueDisclosure.setInvestorReportSent(MailStatus.SENDING.name());
                break;
        }
    }

    private void dealNetValueDisclosure(NetValueDisclosure netValueDisclosure, SendMailInfo sendMailInfo, Cron cron, List<LogCustodianRecords> logCustodianRecordsList, List<LogMailSendRecords> logMailSendRecordsList){
        Objects.requireNonNull(netValueDisclosure, "净值披露不得为空");
        Objects.requireNonNull(cron, "调度任务不得为空");
        String jobName = cron.getClassName();
        switch (jobName){
            case "CustodianBankMailSendJob":
                String custodyReconciliationEmailSent = netValueDisclosure.getCustodyReconciliationEmailSent();
                if (!MailStatus.NONE.name().equals(custodyReconciliationEmailSent)) {
                    netValueDisclosure.setCustodyReconciliationEmailSent(sendMailInfo.getStatus());
                }
                LogCustodianRecords logCustodianRecords = composeCustodianLog(sendMailInfo, netValueDisclosure);
                logCustodianRecordsList.add(logCustodianRecords);
                break;
            case "InvestorMailSendJob":
                netValueDisclosure.setValuationTableSent(sendMailInfo.getStatus());
                LogMailSendRecords logMailSendRecords = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(logMailSendRecords);
                break;
            case "NetValueDisclosureMailSendJob":
                // 检查当前状态，如果是2（锁定状态）或0（未披露），则更新为1（已披露）
                Integer currentStatus = netValueDisclosure.getNetValueDisclosed();
                if (currentStatus != null && (currentStatus == 2 || currentStatus == 0)) {
                    netValueDisclosure.setNetValueDisclosed(1);
                }
                LogMailSendRecords netLogMailSendRecords = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(netLogMailSendRecords);
                break;
            case "ThirdMailSendJob":
                netValueDisclosure.setThirdPartySent(sendMailInfo.getStatus());
                LogMailSendRecords logMailSendRecordsThird = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(logMailSendRecordsThird);
                break;
            case "InvestorStatementMailSendJob":
                netValueDisclosure.setInvestorReportSent(sendMailInfo.getStatus());
                LogMailSendRecords logMailSendRecordsReport = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(logMailSendRecordsReport);
                break;
        }
    }

    private LogCustodianRecords composeCustodianLog(SendMailInfo sendMailInfo, NetValueDisclosure netValueDisclosure){
        Objects.requireNonNull(sendMailInfo,"邮件信息为空");
        Objects.requireNonNull(netValueDisclosure,"邮件信息为空");
        LogCustodianRecords logCustodianRecords = new LogCustodianRecords();
        logCustodianRecords.setMailSendTime(new Date());
        logCustodianRecords.setProductId(netValueDisclosure.getProductId());
        logCustodianRecords.setValuationDate(netValueDisclosure.getValuationDate());
        logCustodianRecords.setCustodianStatus(sendMailInfo.getStatus());
        logCustodianRecords.setCustodianOperator(BaseConstant.DEFAULT_USERNAME);
        logCustodianRecords.setMailId(sendMailInfo.getLogId());
        return logCustodianRecords;
    }

    private LogMailSendRecords composeMailSendLog(SendMailInfo sendMailInfo, NetValueDisclosure netValueDisclosure){
        Objects.requireNonNull(sendMailInfo, "邮件信息为空");
        LogMailSendRecords logMailSendRecords = new LogMailSendRecords();
        logMailSendRecords.setProductId(netValueDisclosure.getProductId());
        logMailSendRecords.setValuationDate(netValueDisclosure.getValuationDate());
        logMailSendRecords.setSendMethod(DisclosureMethod.MAIL.name());
        MailContentHandler mailContentHandler = MailContentHandler.valueOf(sendMailInfo.getHandler());
        logMailSendRecords.setSendContent(mailContentHandler.name());
        logMailSendRecords.setSendTo(sendMailInfo.getContactName());
        logMailSendRecords.setSendTime(new Date());
        logMailSendRecords.setMailId(sendMailInfo.getLogId());
        logMailSendRecords.setStatus(sendMailInfo.getStatus());
        logMailSendRecords.setContactType(sendMailInfo.getContactType());
        return logMailSendRecords;
    }

    /**
     * 回滚净值披露锁定状态
     */
    private void rollbackDisclosureLock(List<NetValueDisclosure> list) {
        try {
            for (NetValueDisclosure netValueDisclosure : list) {
                if (netValueDisclosure.getNetValueDisclosed() != null &&
                    netValueDisclosure.getNetValueDisclosed() == 2) {
                    netValueDisclosure.setNetValueDisclosed(0); // 回滚为未披露状态
                }
            }
            netValueDisclosureService.saveOrUpdateBatch(list);
            log.info("已回滚净值披露锁定状态");
        } catch (Exception e) {
            log.error("回滚净值披露锁定状态失败", e);
        }
    }

    @Override
    public void execute(JobExecutionContext context) {
        if (context != null) {
            log.info("开始执行邮件发送");
            MailSendJob mailSendJob = (MailSendJob) AopContext.currentProxy();
            mailSendJob.doExecute(context);
        }else {
            log.error("无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("MailSendJob", MailSendJob.class);
    }
}
